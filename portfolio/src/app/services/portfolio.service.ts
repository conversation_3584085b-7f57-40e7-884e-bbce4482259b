import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  PersonalInfo, 
  Education, 
  Technology, 
  Project, 
  Experience, 
  Award, 
  Activity 
} from '../models/portfolio.models';

@Injectable({
  providedIn: 'root'
})
export class PortfolioService {
  
  // Sample data - replace with your actual information
  private personalInfo: PersonalInfo = {
    name: 'Your Name',
    title: 'Software Engineering Student',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'Your City, Country',
    linkedin: 'https://linkedin.com/in/yourprofile',
    github: 'https://github.com/yourusername',
    website: 'https://yourwebsite.com',
    bio: 'Passionate software engineering student with experience in full-stack development, mobile applications, and modern web technologies. Always eager to learn new technologies and solve complex problems.',
    profileImage: 'assets/images/profile.jpg'
  };

  private education: Education[] = [
    {
      id: '1',
      institution: 'Your University',
      degree: 'Bachelor of Science',
      field: 'Software Engineering',
      startDate: '2021-09',
      endDate: '2025-06',
      gpa: '3.8/4.0',
      description: 'Relevant coursework: Data Structures, Algorithms, Software Design, Database Systems, Web Development, Mobile Development',
      logo: 'assets/images/university-logo.png'
    }
  ];

  private technologies: Technology[] = [
    // Frontend
    { id: '1', name: 'Angular', category: 'Frontend', proficiency: 'Advanced', icon: 'assets/icons/angular.svg', yearsOfExperience: 2 },
    { id: '2', name: 'React', category: 'Frontend', proficiency: 'Intermediate', icon: 'assets/icons/react.svg', yearsOfExperience: 1 },
    { id: '3', name: 'TypeScript', category: 'Frontend', proficiency: 'Advanced', icon: 'assets/icons/typescript.svg', yearsOfExperience: 2 },
    { id: '4', name: 'HTML5', category: 'Frontend', proficiency: 'Expert', icon: 'assets/icons/html5.svg', yearsOfExperience: 3 },
    { id: '5', name: 'CSS3', category: 'Frontend', proficiency: 'Advanced', icon: 'assets/icons/css3.svg', yearsOfExperience: 3 },
    { id: '6', name: 'SCSS', category: 'Frontend', proficiency: 'Advanced', icon: 'assets/icons/sass.svg', yearsOfExperience: 2 },
    
    // Backend
    { id: '7', name: 'Node.js', category: 'Backend', proficiency: 'Intermediate', icon: 'assets/icons/nodejs.svg', yearsOfExperience: 1 },
    { id: '8', name: 'Python', category: 'Backend', proficiency: 'Advanced', icon: 'assets/icons/python.svg', yearsOfExperience: 2 },
    { id: '9', name: 'Java', category: 'Backend', proficiency: 'Intermediate', icon: 'assets/icons/java.svg', yearsOfExperience: 2 },
    { id: '10', name: 'C++', category: 'Backend', proficiency: 'Intermediate', icon: 'assets/icons/cpp.svg', yearsOfExperience: 1 },
    
    // Database
    { id: '11', name: 'PostgreSQL', category: 'Database', proficiency: 'Intermediate', icon: 'assets/icons/postgresql.svg', yearsOfExperience: 1 },
    { id: '12', name: 'MongoDB', category: 'Database', proficiency: 'Beginner', icon: 'assets/icons/mongodb.svg', yearsOfExperience: 1 },
    
    // DevOps
    { id: '13', name: 'Git', category: 'DevOps', proficiency: 'Advanced', icon: 'assets/icons/git.svg', yearsOfExperience: 3 },
    { id: '14', name: 'Docker', category: 'DevOps', proficiency: 'Beginner', icon: 'assets/icons/docker.svg', yearsOfExperience: 1 },
    
    // Mobile
    { id: '15', name: 'Flutter', category: 'Mobile', proficiency: 'Intermediate', icon: 'assets/icons/flutter.svg', yearsOfExperience: 1 }
  ];

  private projects: Project[] = [
    {
      id: '1',
      title: 'E-Commerce Web Application',
      description: 'Full-stack e-commerce platform with user authentication, product catalog, and payment integration',
      longDescription: 'A comprehensive e-commerce solution built with modern web technologies. Features include user registration/login, product browsing, shopping cart, order management, and secure payment processing.',
      technologies: ['Angular', 'Node.js', 'PostgreSQL', 'TypeScript', 'SCSS'],
      startDate: '2023-09',
      endDate: '2023-12',
      status: 'Completed',
      githubUrl: 'https://github.com/yourusername/ecommerce-app',
      liveUrl: 'https://your-ecommerce-demo.com',
      images: ['assets/images/project1-1.jpg', 'assets/images/project1-2.jpg'],
      features: [
        'User authentication and authorization',
        'Product catalog with search and filtering',
        'Shopping cart and checkout process',
        'Order history and tracking',
        'Admin dashboard for product management'
      ],
      challenges: ['Implementing secure payment processing', 'Optimizing database queries for large product catalogs'],
      learnings: ['Advanced Angular concepts', 'Backend API design', 'Database optimization']
    },
    {
      id: '2',
      title: 'Mobile Task Management App',
      description: 'Cross-platform mobile app for task and project management with real-time synchronization',
      longDescription: 'A productivity app that helps users organize their tasks and projects. Built with Flutter for cross-platform compatibility and Firebase for real-time data synchronization.',
      technologies: ['Flutter', 'Dart', 'Firebase', 'Provider'],
      startDate: '2024-01',
      endDate: '2024-03',
      status: 'Completed',
      githubUrl: 'https://github.com/yourusername/task-manager-app',
      images: ['assets/images/project2-1.jpg', 'assets/images/project2-2.jpg'],
      features: [
        'Create and organize tasks',
        'Project categorization',
        'Real-time synchronization across devices',
        'Push notifications for deadlines',
        'Dark/light theme support'
      ],
      challenges: ['Implementing offline functionality', 'Managing state across complex UI'],
      learnings: ['Mobile app development', 'State management patterns', 'Firebase integration']
    }
  ];

  private experiences: Experience[] = [
    {
      id: '1',
      company: 'Tech Startup Inc.',
      position: 'Software Development Intern',
      type: 'Internship',
      startDate: '2023-06',
      endDate: '2023-08',
      current: false,
      location: 'Remote',
      description: 'Worked on developing and maintaining web applications using modern JavaScript frameworks',
      responsibilities: [
        'Developed responsive web components using React and TypeScript',
        'Collaborated with senior developers on feature implementation',
        'Participated in code reviews and agile development processes',
        'Fixed bugs and improved application performance'
      ],
      achievements: [
        'Improved page load time by 25% through code optimization',
        'Successfully delivered 3 major features ahead of schedule',
        'Received positive feedback from team lead for code quality'
      ],
      technologies: ['React', 'TypeScript', 'Node.js', 'Git'],
      companyLogo: 'assets/images/company1-logo.png'
    }
  ];

  private awards: Award[] = [
    {
      id: '1',
      title: 'Dean\'s List',
      organization: 'Your University',
      date: '2023-12',
      description: 'Recognized for academic excellence with GPA above 3.5',
      category: 'Academic',
      image: 'assets/images/deans-list.jpg'
    },
    {
      id: '2',
      title: 'Best Mobile App - University Hackathon',
      organization: 'Your University Tech Club',
      date: '2023-10',
      description: 'Won first place for developing an innovative mobile application during 48-hour hackathon',
      category: 'Competition',
      image: 'assets/images/hackathon-award.jpg'
    }
  ];

  private activities: Activity[] = [
    {
      id: '1',
      title: 'Computer Science Club',
      organization: 'Your University',
      role: 'Vice President',
      startDate: '2022-09',
      current: true,
      description: 'Leading initiatives to promote computer science education and organize tech events',
      achievements: [
        'Organized 5 technical workshops with 200+ attendees',
        'Increased club membership by 40%',
        'Established partnerships with local tech companies'
      ],
      image: 'assets/images/cs-club.jpg'
    },
    {
      id: '2',
      title: 'Volunteer Coding Instructor',
      organization: 'Local Community Center',
      role: 'Instructor',
      startDate: '2023-01',
      current: true,
      description: 'Teaching basic programming concepts to high school students',
      achievements: [
        'Taught programming to 30+ students',
        'Developed curriculum for beginner Python course',
        'Mentored students in their first coding projects'
      ],
      image: 'assets/images/volunteer-teaching.jpg'
    }
  ];

  // Observables for reactive data
  private personalInfoSubject = new BehaviorSubject<PersonalInfo>(this.personalInfo);
  private educationSubject = new BehaviorSubject<Education[]>(this.education);
  private technologiesSubject = new BehaviorSubject<Technology[]>(this.technologies);
  private projectsSubject = new BehaviorSubject<Project[]>(this.projects);
  private experiencesSubject = new BehaviorSubject<Experience[]>(this.experiences);
  private awardsSubject = new BehaviorSubject<Award[]>(this.awards);
  private activitiesSubject = new BehaviorSubject<Activity[]>(this.activities);

  // Public methods to get data
  getPersonalInfo(): Observable<PersonalInfo> {
    return this.personalInfoSubject.asObservable();
  }

  getEducation(): Observable<Education[]> {
    return this.educationSubject.asObservable();
  }

  getTechnologies(): Observable<Technology[]> {
    return this.technologiesSubject.asObservable();
  }

  getProjects(): Observable<Project[]> {
    return this.projectsSubject.asObservable();
  }

  getExperiences(): Observable<Experience[]> {
    return this.experiencesSubject.asObservable();
  }

  getAwards(): Observable<Award[]> {
    return this.awardsSubject.asObservable();
  }

  getActivities(): Observable<Activity[]> {
    return this.activitiesSubject.asObservable();
  }

  // Helper methods
  getTechnologiesByCategory(category: string): Technology[] {
    return this.technologies.filter(tech => tech.category === category);
  }

  getFeaturedProjects(count: number = 3): Project[] {
    return this.projects.slice(0, count);
  }

  getCurrentExperiences(): Experience[] {
    return this.experiences.filter(exp => exp.current);
  }
}
