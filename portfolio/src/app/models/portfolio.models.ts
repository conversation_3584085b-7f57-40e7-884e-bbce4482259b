export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  linkedin: string;
  github: string;
  website?: string;
  bio: string;
  profileImage: string;
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  gpa?: string;
  description: string;
  logo?: string;
}

export interface Technology {
  id: string;
  name: string;
  category: 'Frontend' | 'Backend' | 'Database' | 'DevOps' | 'Mobile' | 'Other';
  proficiency: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  icon?: string;
  yearsOfExperience?: number;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  startDate: string;
  endDate?: string;
  status: 'Completed' | 'In Progress' | 'Planned';
  githubUrl?: string;
  liveUrl?: string;
  images: string[];
  features: string[];
  challenges?: string[];
  learnings?: string[];
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  type: 'Internship' | 'Part-time' | 'Full-time' | 'Contract' | 'Volunteer';
  startDate: string;
  endDate?: string;
  current: boolean;
  location: string;
  description: string;
  responsibilities: string[];
  achievements: string[];
  technologies: string[];
  companyLogo?: string;
}

export interface Award {
  id: string;
  title: string;
  organization: string;
  date: string;
  description: string;
  category: 'Academic' | 'Competition' | 'Recognition' | 'Certification';
  image?: string;
  url?: string;
}

export interface Activity {
  id: string;
  title: string;
  organization: string;
  role: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  achievements: string[];
  image?: string;
}

export interface Skill {
  id: string;
  name: string;
  category: string;
  level: number; // 1-5 scale
}

export interface Contact {
  name: string;
  email: string;
  subject: string;
  message: string;
}
