<mat-sidenav-container class="sidenav-container">
  <mat-sidenav #drawer class="sidenav" fixedInViewport
      [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
      [mode]="(isHandset$ | async) ? 'over' : 'side'"
      [opened]="(isHandset$ | async) === false">
    <mat-toolbar class="sidenav-toolbar">Portfolio</mat-toolbar>
    <mat-nav-list>
      <a mat-list-item
         *ngFor="let item of navigationItems"
         [routerLink]="item.route"
         routerLinkActive="active-link"
         (click)="drawer.close()">
        <mat-icon matListItemIcon>{{item.icon}}</mat-icon>
        <span matListItemTitle>{{item.label}}</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <mat-toolbar color="primary" class="main-toolbar">
      <button
        type="button"
        aria-label="Toggle sidenav"
        mat-icon-button
        (click)="drawer.toggle()"
        *ngIf="isHandset$ | async">
        <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
      </button>
      <span class="toolbar-title">Your Portfolio</span>

      <!-- Desktop Navigation -->
      <div class="desktop-nav" *ngIf="(isHandset$ | async) === false">
        <button mat-button
                *ngFor="let item of navigationItems"
                [routerLink]="item.route"
                routerLinkActive="active-button">
          <mat-icon>{{item.icon}}</mat-icon>
          {{item.label}}
        </button>
      </div>
    </mat-toolbar>

    <!-- Main content area -->
    <div class="main-content">
      <ng-content></ng-content>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
