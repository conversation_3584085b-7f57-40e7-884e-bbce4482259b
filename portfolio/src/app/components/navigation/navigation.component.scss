.sidenav-container {
  height: 100vh;
}

.sidenav {
  width: 250px;
  background: #fafafa;

  .sidenav-toolbar {
    background: #673ab7;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
  }
}

.main-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .toolbar-title {
    flex: 1;
    font-weight: 600;
    font-size: 1.3rem;
  }

  .desktop-nav {
    display: flex;
    gap: 8px;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      color: white;

      &.active-button {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
      }
    }
  }
}

.main-content {
  padding: 0;
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

.active-link {
  background: rgba(103, 58, 183, 0.1) !important;
  color: #673ab7 !important;

  mat-icon {
    color: #673ab7 !important;
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .main-content {
    min-height: calc(100vh - 56px);
  }
}