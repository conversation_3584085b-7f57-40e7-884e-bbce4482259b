.home-container {
  width: 100%;
}

// Hero Section
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  min-height: 70vh;
  display: flex;
  align-items: center;

  .hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
  }

  .hero-text {
    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 16px;
      line-height: 1.2;

      .highlight {
        color: #ffd54f;
      }
    }

    .hero-subtitle {
      font-size: 1.5rem;
      font-weight: 400;
      margin-bottom: 24px;
      opacity: 0.9;
    }

    .hero-description {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 32px;
      opacity: 0.9;
    }

    .hero-actions {
      display: flex;
      gap: 16px;
      margin-bottom: 32px;
      flex-wrap: wrap;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        font-size: 1rem;
      }
    }

    .social-links {
      display: flex;
      gap: 8px;

      a {
        color: white;
        opacity: 0.8;
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .hero-image {
    display: flex;
    justify-content: center;

    .profile-image-container {
      position: relative;

      .profile-image {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        object-fit: cover;
        border: 6px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

// Common section styles
.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 48px;
  color: #333;
}

.section-action {
  text-align: center;
  margin-top: 48px;

  button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }
}

// Technologies Section
.technologies-section {
  padding: 80px 0;
  background: white;

  .technologies-grid {
    display: flex;
    justify-content: center;

    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 12px;

      .tech-chip {
        font-size: 1rem;
        padding: 8px 16px;
        background: #f5f5f5;
        color: #333;
        border: 2px solid #e0e0e0;
        transition: all 0.3s ease;

        &:hover {
          background: #673ab7;
          color: white;
          border-color: #673ab7;
        }
      }
    }
  }
}

// Projects Section
.projects-section {
  padding: 80px 0;
  background: #f8f9fa;

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
    margin-bottom: 48px;

    .project-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
      }

      mat-card-header {
        margin-bottom: 16px;
      }

      .project-technologies {
        margin-top: 16px;

        mat-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          mat-chip {
            font-size: 0.875rem;
            background: #e3f2fd;
            color: #1976d2;
          }
        }
      }

      mat-card-actions {
        display: flex;
        gap: 8px;

        button {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }
}

// CTA Section
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 16px;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 32px;
    opacity: 0.9;
  }

  button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 32px;
    font-size: 1.1rem;
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
    min-height: auto;

    .hero-content {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .hero-text {
      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.3rem;
      }

      .hero-actions {
        justify-content: center;
      }
    }

    .hero-image .profile-image {
      width: 250px;
      height: 250px;
    }
  }

  .section-title {
    font-size: 2rem;
  }

  .projects-grid {
    grid-template-columns: 1fr !important;
  }

  .technologies-section,
  .projects-section,
  .cta-section {
    padding: 60px 0;
  }
}