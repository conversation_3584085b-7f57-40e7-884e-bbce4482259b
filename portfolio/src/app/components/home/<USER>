import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { PortfolioService } from '../../services/portfolio.service';
import { PersonalInfo, Project, Technology } from '../../models/portfolio.models';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  personalInfo$: Observable<PersonalInfo>;
  featuredProjects: Project[] = [];
  technologies: Technology[] = [];

  constructor(private portfolioService: PortfolioService) {
    this.personalInfo$ = this.portfolioService.getPersonalInfo();
  }

  ngOnInit(): void {
    this.featuredProjects = this.portfolioService.getFeaturedProjects(2);
    this.portfolioService.getTechnologies().subscribe(techs => {
      this.technologies = techs.filter(tech => tech.proficiency === 'Advanced' || tech.proficiency === 'Expert').slice(0, 6);
    });
  }
}
