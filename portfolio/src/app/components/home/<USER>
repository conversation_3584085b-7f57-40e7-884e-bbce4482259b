<div class="home-container" *ngIf="personalInfo$ | async as personalInfo">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          Hi, I'm <span class="highlight">{{personalInfo.name}}</span>
        </h1>
        <h2 class="hero-subtitle">{{personalInfo.title}}</h2>
        <p class="hero-description">{{personalInfo.bio}}</p>

        <div class="hero-actions">
          <button mat-raised-button color="primary" routerLink="/projects">
            <mat-icon>work</mat-icon>
            View My Work
          </button>
          <button mat-stroked-button color="primary" routerLink="/contact">
            <mat-icon>contact_mail</mat-icon>
            Get In Touch
          </button>
        </div>

        <div class="social-links">
          <a [href]="personalInfo.github" target="_blank" mat-icon-button>
            <mat-icon>code</mat-icon>
          </a>
          <a [href]="personalInfo.linkedin" target="_blank" mat-icon-button>
            <mat-icon>business</mat-icon>
          </a>
          <a [href]="'mailto:' + personalInfo.email" mat-icon-button>
            <mat-icon>email</mat-icon>
          </a>
        </div>
      </div>

      <div class="hero-image">
        <div class="profile-image-container">
          <img [src]="personalInfo.profileImage" [alt]="personalInfo.name" class="profile-image">
        </div>
      </div>
    </div>
  </section>

  <!-- Technologies Section -->
  <section class="technologies-section">
    <div class="section-content">
      <h2 class="section-title">Technologies I Work With</h2>
      <div class="technologies-grid">
        <mat-chip-set>
          <mat-chip *ngFor="let tech of technologies" class="tech-chip">
            {{tech.name}}
          </mat-chip>
        </mat-chip-set>
      </div>
      <div class="section-action">
        <button mat-button color="primary" routerLink="/about">
          View All Skills <mat-icon>arrow_forward</mat-icon>
        </button>
      </div>
    </div>
  </section>

  <!-- Featured Projects Section -->
  <section class="projects-section">
    <div class="section-content">
      <h2 class="section-title">Featured Projects</h2>
      <div class="projects-grid">
        <mat-card *ngFor="let project of featuredProjects" class="project-card">
          <mat-card-header>
            <mat-card-title>{{project.title}}</mat-card-title>
            <mat-card-subtitle>{{project.status}}</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <p>{{project.description}}</p>
            <div class="project-technologies">
              <mat-chip-set>
                <mat-chip *ngFor="let tech of project.technologies.slice(0, 3)">
                  {{tech}}
                </mat-chip>
              </mat-chip-set>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button color="primary" *ngIf="project.githubUrl" [href]="project.githubUrl" target="_blank">
              <mat-icon>code</mat-icon>
              Code
            </button>
            <button mat-button color="primary" *ngIf="project.liveUrl" [href]="project.liveUrl" target="_blank">
              <mat-icon>launch</mat-icon>
              Live Demo
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <div class="section-action">
        <button mat-raised-button color="primary" routerLink="/projects">
          View All Projects <mat-icon>arrow_forward</mat-icon>
        </button>
      </div>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="section-content">
      <h2>Let's Work Together</h2>
      <p>I'm always interested in new opportunities and exciting projects.</p>
      <button mat-raised-button color="accent" routerLink="/contact">
        <mat-icon>contact_mail</mat-icon>
        Contact Me
      </button>
    </div>
  </section>
</div>
