export declare const A = 65;

export declare const ALT = 18;

export declare const APOSTROPHE = 192;

export declare const AT_SIGN = 64;

export declare const B = 66;

export declare const BACKSLASH = 220;

export declare const BACKSPACE = 8;

export declare const C = 67;

export declare const CAPS_LOCK = 20;

export declare const CLOSE_SQUARE_BRACKET = 221;

export declare const COMMA = 188;

export declare const CONTEXT_MENU = 93;

export declare const CONTROL = 17;

export declare const D = 68;

export declare const DASH = 189;

export declare const DELETE = 46;

export declare const DOWN_ARROW = 40;

export declare const E = 69;

export declare const EIGHT = 56;

export declare const END = 35;

export declare const ENTER = 13;

export declare const EQUALS = 187;

export declare const ESCAPE = 27;

export declare const F = 70;

export declare const F1 = 112;

export declare const F10 = 121;

export declare const F11 = 122;

export declare const F12 = 123;

export declare const F2 = 113;

export declare const F3 = 114;

export declare const F4 = 115;

export declare const F5 = 116;

export declare const F6 = 117;

export declare const F7 = 118;

export declare const F8 = 119;

export declare const F9 = 120;

export declare const FF_EQUALS = 61;

export declare const FF_MINUS = 173;

export declare const FF_MUTE = 181;

export declare const FF_SEMICOLON = 59;

export declare const FF_VOLUME_DOWN = 182;

export declare const FF_VOLUME_UP = 183;

export declare const FIRST_MEDIA = 166;

export declare const FIVE = 53;

export declare const FOUR = 52;

export declare const G = 71;

export declare const H = 72;

/**
 * Checks whether a modifier key is pressed.
 * @param event Event to be checked.
 */
export declare function hasModifierKey(event: KeyboardEvent, ...modifiers: ModifierKey[]): boolean;

export declare const HOME = 36;

export declare const I = 73;

export declare const INSERT = 45;

export declare const J = 74;

export declare const K = 75;

export declare const L = 76;

export declare const LAST_MEDIA = 183;

export declare const LEFT_ARROW = 37;

export declare const M = 77;


export declare const MAC_ENTER = 3;

export declare const MAC_META = 224;

export declare const MAC_WK_CMD_LEFT = 91;

export declare const MAC_WK_CMD_RIGHT = 93;

export declare const META = 91;


export declare type ModifierKey = 'altKey' | 'shiftKey' | 'ctrlKey' | 'metaKey';

export declare const MUTE = 173;

export declare const N = 78;

export declare const NINE = 57;

export declare const NUM_CENTER = 12;

export declare const NUM_LOCK = 144;

export declare const NUMPAD_DIVIDE = 111;

export declare const NUMPAD_EIGHT = 104;

export declare const NUMPAD_FIVE = 101;

export declare const NUMPAD_FOUR = 100;

export declare const NUMPAD_MINUS = 109;

export declare const NUMPAD_MULTIPLY = 106;

export declare const NUMPAD_NINE = 105;

export declare const NUMPAD_ONE = 97;

export declare const NUMPAD_PERIOD = 110;

export declare const NUMPAD_PLUS = 107;

export declare const NUMPAD_SEVEN = 103;

export declare const NUMPAD_SIX = 102;

export declare const NUMPAD_THREE = 99;

export declare const NUMPAD_TWO = 98;

export declare const NUMPAD_ZERO = 96;

export declare const O = 79;

export declare const ONE = 49;

export declare const OPEN_SQUARE_BRACKET = 219;

export declare const P = 80;

export declare const PAGE_DOWN = 34;

export declare const PAGE_UP = 33;

export declare const PAUSE = 19;

export declare const PERIOD = 190;

export declare const PLUS_SIGN = 43;

export declare const PRINT_SCREEN = 44;

export declare const Q = 81;

export declare const QUESTION_MARK = 63;

export declare const R = 82;

export declare const RIGHT_ARROW = 39;

export declare const S = 83;

export declare const SCROLL_LOCK = 145;

export declare const SEMICOLON = 186;

export declare const SEVEN = 55;

export declare const SHIFT = 16;

export declare const SINGLE_QUOTE = 222;

export declare const SIX = 54;

export declare const SLASH = 191;

export declare const SPACE = 32;

export declare const T = 84;

export declare const TAB = 9;

export declare const THREE = 51;

export declare const TILDE = 192;

export declare const TWO = 50;

export declare const U = 85;

export declare const UP_ARROW = 38;

export declare const V = 86;

export declare const VOLUME_DOWN = 174;

export declare const VOLUME_UP = 175;

export declare const W = 87;

export declare const X = 88;

export declare const Y = 89;

export declare const Z = 90;

export declare const ZERO = 48;

export { }
