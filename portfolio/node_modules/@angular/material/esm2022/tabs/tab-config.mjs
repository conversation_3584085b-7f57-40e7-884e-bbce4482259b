/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/** Injection token that can be used to provide the default options the tabs module. */
export const MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');
//# sourceMappingURL=data:application/json;base64,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