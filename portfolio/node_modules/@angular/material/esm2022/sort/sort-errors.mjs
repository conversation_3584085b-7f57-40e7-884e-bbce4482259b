/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** @docs-private */
export function getSortDuplicateSortableIdError(id) {
    return Error(`Cannot have two MatSortables with the same id (${id}).`);
}
/** @docs-private */
export function getSortHeaderNotContainedWithinSortError() {
    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);
}
/** @docs-private */
export function getSortHeaderMissingIdError() {
    return Error(`MatSortHeader must be provided with a unique id.`);
}
/** @docs-private */
export function getSortInvalidDirectionError(direction) {
    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic29ydC1lcnJvcnMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwvc29ydC9zb3J0LWVycm9ycy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxvQkFBb0I7QUFDcEIsTUFBTSxVQUFVLCtCQUErQixDQUFDLEVBQVU7SUFDeEQsT0FBTyxLQUFLLENBQUMsa0RBQWtELEVBQUUsSUFBSSxDQUFDLENBQUM7QUFDekUsQ0FBQztBQUVELG9CQUFvQjtBQUNwQixNQUFNLFVBQVUsd0NBQXdDO0lBQ3RELE9BQU8sS0FBSyxDQUFDLGtGQUFrRixDQUFDLENBQUM7QUFDbkcsQ0FBQztBQUVELG9CQUFvQjtBQUNwQixNQUFNLFVBQVUsMkJBQTJCO0lBQ3pDLE9BQU8sS0FBSyxDQUFDLGtEQUFrRCxDQUFDLENBQUM7QUFDbkUsQ0FBQztBQUVELG9CQUFvQjtBQUNwQixNQUFNLFVBQVUsNEJBQTRCLENBQUMsU0FBaUI7SUFDNUQsT0FBTyxLQUFLLENBQUMsR0FBRyxTQUFTLG1EQUFtRCxDQUFDLENBQUM7QUFDaEYsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKiogQGRvY3MtcHJpdmF0ZSAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFNvcnREdXBsaWNhdGVTb3J0YWJsZUlkRXJyb3IoaWQ6IHN0cmluZyk6IEVycm9yIHtcbiAgcmV0dXJuIEVycm9yKGBDYW5ub3QgaGF2ZSB0d28gTWF0U29ydGFibGVzIHdpdGggdGhlIHNhbWUgaWQgKCR7aWR9KS5gKTtcbn1cblxuLyoqIEBkb2NzLXByaXZhdGUgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRTb3J0SGVhZGVyTm90Q29udGFpbmVkV2l0aGluU29ydEVycm9yKCk6IEVycm9yIHtcbiAgcmV0dXJuIEVycm9yKGBNYXRTb3J0SGVhZGVyIG11c3QgYmUgcGxhY2VkIHdpdGhpbiBhIHBhcmVudCBlbGVtZW50IHdpdGggdGhlIE1hdFNvcnQgZGlyZWN0aXZlLmApO1xufVxuXG4vKiogQGRvY3MtcHJpdmF0ZSAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFNvcnRIZWFkZXJNaXNzaW5nSWRFcnJvcigpOiBFcnJvciB7XG4gIHJldHVybiBFcnJvcihgTWF0U29ydEhlYWRlciBtdXN0IGJlIHByb3ZpZGVkIHdpdGggYSB1bmlxdWUgaWQuYCk7XG59XG5cbi8qKiBAZG9jcy1wcml2YXRlICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0U29ydEludmFsaWREaXJlY3Rpb25FcnJvcihkaXJlY3Rpb246IHN0cmluZyk6IEVycm9yIHtcbiAgcmV0dXJuIEVycm9yKGAke2RpcmVjdGlvbn0gaXMgbm90IGEgdmFsaWQgc29ydCBkaXJlY3Rpb24gKCdhc2MnIG9yICdkZXNjJykuYCk7XG59XG4iXX0=