/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// primary entry-point which is empty as of version 9. All components should
// be imported through their individual entry-points. This file is needed to
// satisfy the "ng_package" bazel rule which also requires a primary entry-point.
// Workaround for: https://github.com/microsoft/rushstack/issues/2806.
// This is a private export that can be removed at any time.
export const ɵɵtsModuleIndicatorApiExtractorWorkaround = true;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsNEVBQTRFO0FBQzVFLDRFQUE0RTtBQUM1RSxpRkFBaUY7QUFFakYsc0VBQXNFO0FBQ3RFLDREQUE0RDtBQUM1RCxNQUFNLENBQUMsTUFBTSx5Q0FBeUMsR0FBRyxJQUFJLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuLy8gcHJpbWFyeSBlbnRyeS1wb2ludCB3aGljaCBpcyBlbXB0eSBhcyBvZiB2ZXJzaW9uIDkuIEFsbCBjb21wb25lbnRzIHNob3VsZFxuLy8gYmUgaW1wb3J0ZWQgdGhyb3VnaCB0aGVpciBpbmRpdmlkdWFsIGVudHJ5LXBvaW50cy4gVGhpcyBmaWxlIGlzIG5lZWRlZCB0b1xuLy8gc2F0aXNmeSB0aGUgXCJuZ19wYWNrYWdlXCIgYmF6ZWwgcnVsZSB3aGljaCBhbHNvIHJlcXVpcmVzIGEgcHJpbWFyeSBlbnRyeS1wb2ludC5cblxuLy8gV29ya2Fyb3VuZCBmb3I6IGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvcnVzaHN0YWNrL2lzc3Vlcy8yODA2LlxuLy8gVGhpcyBpcyBhIHByaXZhdGUgZXhwb3J0IHRoYXQgY2FuIGJlIHJlbW92ZWQgYXQgYW55IHRpbWUuXG5leHBvcnQgY29uc3QgybXJtXRzTW9kdWxlSW5kaWNhdG9yQXBpRXh0cmFjdG9yV29ya2Fyb3VuZCA9IHRydWU7XG4iXX0=