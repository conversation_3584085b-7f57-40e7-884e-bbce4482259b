/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ComponentHarness } from '@angular/cdk/testing';
/**
 * Base class for custom form-field control harnesses. Harnesses for
 * custom controls with form-fields need to implement this interface.
 */
export class MatFormFieldControlHarness extends ComponentHarness {
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZm9ybS1maWVsZC1jb250cm9sLWhhcm5lc3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwvZm9ybS1maWVsZC90ZXN0aW5nL2NvbnRyb2wvZm9ybS1maWVsZC1jb250cm9sLWhhcm5lc3MudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLGdCQUFnQixFQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFFdEQ7OztHQUdHO0FBQ0gsTUFBTSxPQUFnQiwwQkFBMkIsU0FBUSxnQkFBZ0I7Q0FBRyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge0NvbXBvbmVudEhhcm5lc3N9IGZyb20gJ0Bhbmd1bGFyL2Nkay90ZXN0aW5nJztcblxuLyoqXG4gKiBCYXNlIGNsYXNzIGZvciBjdXN0b20gZm9ybS1maWVsZCBjb250cm9sIGhhcm5lc3Nlcy4gSGFybmVzc2VzIGZvclxuICogY3VzdG9tIGNvbnRyb2xzIHdpdGggZm9ybS1maWVsZHMgbmVlZCB0byBpbXBsZW1lbnQgdGhpcyBpbnRlcmZhY2UuXG4gKi9cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBNYXRGb3JtRmllbGRDb250cm9sSGFybmVzcyBleHRlbmRzIENvbXBvbmVudEhhcm5lc3Mge31cbiJdfQ==