/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { MatInput } from './input';
export { MatInputModule } from './module';
export * from './input-value-accessor';
export * from './input-errors';
// Re-provide these for convenience since they used to be provided implicitly.
export { MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, } from '@angular/material/form-field';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHVibGljLWFwaS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3NyYy9tYXRlcmlhbC9pbnB1dC9wdWJsaWMtYXBpLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxRQUFRLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDakMsT0FBTyxFQUFDLGNBQWMsRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUN4QyxjQUFjLHdCQUF3QixDQUFDO0FBQ3ZDLGNBQWMsZ0JBQWdCLENBQUM7QUFFL0IsOEVBQThFO0FBQzlFLE9BQU8sRUFDTCxZQUFZLEVBQ1osUUFBUSxFQUNSLE9BQU8sRUFDUCxRQUFRLEVBQ1IsU0FBUyxFQUNULFNBQVMsR0FDVixNQUFNLDhCQUE4QixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmV4cG9ydCB7TWF0SW5wdXR9IGZyb20gJy4vaW5wdXQnO1xuZXhwb3J0IHtNYXRJbnB1dE1vZHVsZX0gZnJvbSAnLi9tb2R1bGUnO1xuZXhwb3J0ICogZnJvbSAnLi9pbnB1dC12YWx1ZS1hY2Nlc3Nvcic7XG5leHBvcnQgKiBmcm9tICcuL2lucHV0LWVycm9ycyc7XG5cbi8vIFJlLXByb3ZpZGUgdGhlc2UgZm9yIGNvbnZlbmllbmNlIHNpbmNlIHRoZXkgdXNlZCB0byBiZSBwcm92aWRlZCBpbXBsaWNpdGx5LlxuZXhwb3J0IHtcbiAgTWF0Rm9ybUZpZWxkLFxuICBNYXRMYWJlbCxcbiAgTWF0SGludCxcbiAgTWF0RXJyb3IsXG4gIE1hdFByZWZpeCxcbiAgTWF0U3VmZml4LFxufSBmcm9tICdAYW5ndWxhci9tYXRlcmlhbC9mb3JtLWZpZWxkJztcbiJdfQ==