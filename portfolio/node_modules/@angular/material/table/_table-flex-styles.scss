// Flex-based table structure
$header-row-height: 56px;
$row-height: 48px;
$row-horizontal-padding: 24px;

// Only use tag name selectors here since the styles are shared between MDC and non-MDC
@mixin private-table-flex-styles {
  mat-table {
    display: block;
  }

  mat-header-row {
    min-height: $header-row-height;
  }

  mat-row, mat-footer-row {
    min-height: $row-height;
  }

  mat-row, mat-header-row, mat-footer-row {
    display: flex;
    // Define a border style, but then widths default to 3px. Reset them to 0px except the bottom
    // which should be 1px;
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    align-items: center;
    box-sizing: border-box;
  }

  mat-cell, mat-header-cell, mat-footer-cell {
    // Note: we use `first-of-type`/`last-of-type` here in order to prevent extra
    // elements like ripples or badges from throwing off the layout (see #11165).
    &:first-of-type {
      padding-left: $row-horizontal-padding;

      [dir='rtl'] &:not(:only-of-type) {
        padding-left: 0;
        padding-right: $row-horizontal-padding;
      }
    }

    &:last-of-type {
      padding-right: $row-horizontal-padding;

      [dir='rtl'] &:not(:only-of-type) {
        padding-right: 0;
        padding-left: $row-horizontal-padding;
      }
    }
  }

  mat-cell, mat-header-cell, mat-footer-cell {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
    word-wrap: break-word;
    min-height: inherit;
  }
}
