@use 'sass:map';
@use '@material/tooltip/plain-tooltip-theme' as mdc-plain-tooltip-theme;
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/plain-tooltip' as tokens-mdc-plain-tooltip;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include mdc-plain-tooltip-theme.theme(tokens-mdc-plain-tooltip.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    $mdc-tooltip-color-tokens: tokens-mdc-plain-tooltip.get-color-tokens($theme);

    // Add values for MDC tooltip tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-plain-tooltip-theme.theme($mdc-tooltip-color-tokens);
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    $mdc-tooltip-typography-tokens: tokens-mdc-plain-tooltip.get-typography-tokens($theme);

    // Add values for MDC tooltip tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-plain-tooltip-theme.theme($mdc-tooltip-typography-tokens);
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    $mdc-tooltip-density-tokens: tokens-mdc-plain-tooltip.get-density-tokens($theme);

    // Add values for MDC tooltip tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-plain-tooltip-theme.theme($mdc-tooltip-density-tokens);
    }
  }
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-tooltip') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  @if $tokens != () {
    @include mdc-plain-tooltip-theme.theme(map.get($tokens, tokens-mdc-plain-tooltip.$prefix));
  }
}
