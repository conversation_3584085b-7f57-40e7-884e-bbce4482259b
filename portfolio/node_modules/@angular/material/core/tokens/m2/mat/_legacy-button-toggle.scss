@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, legacy-button-toggle);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    height: 36px,
    shape: 2px,
    focus-state-layer-opacity: 1,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    text-color: inspection.get-theme-color($theme, foreground, hint-text),
    state-layer-color: inspection.get-theme-color($theme, background, focused-button),
    selected-state-text-color: inspection.get-theme-color($theme, foreground, secondary-text),
    selected-state-background-color: inspection.get-theme-color(
        $theme, background, selected-button),
    disabled-state-text-color: inspection.get-theme-color($theme, foreground, disabled-button),
    disabled-state-background-color: inspection.get-theme-color(
        $theme, background, disabled-button-toggle),
    disabled-selected-state-background-color: inspection.get-theme-color(
        $theme, background, selected-disabled-button)
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
