@use '@material/elevation/elevation-theme' as mdc-elevation;
@use 'sass:color';
@use 'sass:meta';
@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, sidenav);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // Currently zero, but it appears to be relevant for M3.
    // See: https://m3.material.io/components/navigation-drawer/overview
    container-shape: 0,
    container-elevation-shadow: mdc-elevation.elevation-box-shadow(16),
    container-width: auto,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) ==  dark;
  $scrim-opacity: 0.6;
  $scrim-color: sass-utils.safe-color-change(
      inspection.get-theme-color($theme, background, card), $alpha: $scrim-opacity);
  $fallback-scrim-color: if($is-dark, rgba(#fff, $scrim-opacity), rgba(#000, $scrim-opacity));

  @return (
    container-divider-color: inspection.get-theme-color($theme, foreground, divider),
    container-background-color: inspection.get-theme-color($theme, background, dialog),
    container-text-color: inspection.get-theme-color($theme, foreground, text),
    content-background-color: inspection.get-theme-color($theme, background, background),
    content-text-color: inspection.get-theme-color($theme, foreground, text),

    // We use invert() here to have the darken the background color expected to be used.
    // If the background is light, we use a dark backdrop. If the background is dark, we
    // use a light backdrop. If the value isn't a color, Sass will throw an error so we
    // fall back to something generic.
    scrim-color: if(meta.type-of($scrim-color) == color,
      color.invert($scrim-color), $fallback-scrim-color),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
