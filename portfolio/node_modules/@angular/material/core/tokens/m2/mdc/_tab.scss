@use 'sass:map';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, tab);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // This is specified both here and in the density tokens, because it determines the size of the
    // tab itself and there are internal tests who don't configure the theme correctly.
    container-height: 48px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    inactive-label-text-color: null,
    active-label-text-color: null,
    container-color: null,
    container-elevation: null,
    container-shadow-color: null,
    container-shape: null,
    focus-label-text-color: null,
    focus-state-layer-color: null,
    focus-state-layer-opacity: null,
    hover-label-text-color: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,
    pressed-label-text-color: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null,
    with-icon-active-icon-color: null,
    with-icon-focus-icon-color: null,
    with-icon-hover-icon-color: null,
    with-icon-inactive-icon-color: null,
    with-icon-pressed-icon-color: null,
    with-icon-icon-size: null,
    label-text-font: null,
    label-text-size: null,
    label-text-letter-spacing: null,
    label-text-line-height: null,
    label-text-weight: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  @return ();
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);

  @return (
    container-height: map.get((
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
    ), $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
