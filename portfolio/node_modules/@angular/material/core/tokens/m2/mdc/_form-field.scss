@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, form-field);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    label-text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    label-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, body-2, letter-spacing),
    label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
