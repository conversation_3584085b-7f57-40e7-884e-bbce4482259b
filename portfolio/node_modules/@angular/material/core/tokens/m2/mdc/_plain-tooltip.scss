@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, plain-tooltip);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // Border radius for the tooltip container.
    container-shape: 4px,
    // Line height of the tooltip text.
    supporting-text-line-height: 16px,
    // MDC does not seem to use these token.
    supporting-text-type: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {

  @return (
    // Color of the tooltip container.
    container-color: inspection.get-theme-color($theme, background, tooltip),
    // Color of the tooltip text.
    supporting-text-color: #fff,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Font for the tooltip text.
    supporting-text-font: inspection.get-theme-typography($theme, caption, font-family),
    // Font size for the tooltip text.
    supporting-text-size: inspection.get-theme-typography($theme, caption, font-size),
    // Font weight of the tooltip text.
    supporting-text-weight: inspection.get-theme-typography($theme, caption, font-weight),
    // Tracking (space between letters) of the tooltip text.
    supporting-text-tracking: inspection.get-theme-typography($theme, caption, letter-spacing),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
