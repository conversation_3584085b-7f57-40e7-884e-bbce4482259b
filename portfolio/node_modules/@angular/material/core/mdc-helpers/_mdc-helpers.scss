// TODO(mmalerba): this file should be split into separate cohesive partials for things like
//  "theming", "typography", "core".
@use '../typography/typography';
@use '@material/feature-targeting' as mdc-feature-targeting;
@use '@material/typography' as mdc-typography;
@use '@material/theme/theme-color' as mdc-theme-color;
@use '@material/theme/css' as mdc-theme-css;

// A set of standard queries to use with MDC's queryable mixins.
$mdc-base-styles-query: mdc-feature-targeting.without(mdc-feature-targeting.any(color, typography));
$mdc-base-styles-without-animation-query:
    mdc-feature-targeting.all($mdc-base-styles-query, mdc-feature-targeting.without(animation));
$mdc-theme-styles-query: color;
$mdc-typography-styles-query: typography;

// MDC logs a warning if the `contrast-tone` function is called with a CSS variable.
// This function falls back to determining the tone based on whether the theme is light or dark.
@function variable-safe-contrast-tone($value, $is-dark) {
  @if ($value == 'dark' or $value == 'light' or type-of($value) == 'color') {
    @return mdc-theme-color.contrast-tone($value);
  }

  @return if($is-dark, 'light', 'dark');
}

// Function to create an Angular Material typography config from MDC's predefined typography levels.
// This is used for components where we accidentally ended up supporting null typography configs
// that were silently falling back to MDC's typography. At the moment of writing this includes
// `dialog`, `slider` and `tooltip`.
// Important! We shouldn't introduce any new usages of this pattern and we should eventually clean
// up any existing usages.
@function private-fallback-typography-from-mdc() {
  // This is very close to what we have in `define-typography-config`, but we can't use it here,
  // because it would cause a circular import and moving it here doesn't make sense.
  $font-family: mdc-typography.$font-family;
  @return (
    font-family: $font-family,
    headline-1: typography.typography-config-level-from-mdc(headline1, $font-family),
    headline-2: typography.typography-config-level-from-mdc(headline2, $font-family),
    headline-3: typography.typography-config-level-from-mdc(headline3, $font-family),
    headline-4: typography.typography-config-level-from-mdc(headline4, $font-family),
    headline-5: typography.typography-config-level-from-mdc(headline5, $font-family),
    headline-6: typography.typography-config-level-from-mdc(headline6, $font-family),
    subtitle-1: typography.typography-config-level-from-mdc(subtitle1, $font-family),
    subtitle-2: typography.typography-config-level-from-mdc(subtitle2, $font-family),
    body-1: typography.typography-config-level-from-mdc(body1, $font-family),
    body-2: typography.typography-config-level-from-mdc(body2, $font-family),
    caption: typography.typography-config-level-from-mdc(caption, $font-family),
    button: typography.typography-config-level-from-mdc(button, $font-family),
    overline: typography.typography-config-level-from-mdc(overline, $font-family),
  );
}

// Disables MDC's CSS custom property fallbacks for the specified mixin content.
@mixin disable-mdc-fallback-declarations {
  $previous-value: mdc-theme-css.$enable-fallback-declarations;
  mdc-theme-css.$enable-fallback-declarations: false;
  @content;
  mdc-theme-css.$enable-fallback-declarations: $previous-value;
}
