@use '../../theming/theming';
@use '../../theming/inspection';
@use '../../theming/validation';
@use '../../style/sass-utils';
@use '../../tokens/token-utils';
@use '../../tokens/m2/mat/full-pseudo-checkbox' as tokens-mat-full-pseudo-checkbox;
@use '../../tokens/m2/mat/minimal-pseudo-checkbox' as tokens-mat-minimal-pseudo-checkbox;

@mixin _palette-styles($theme, $palette-name) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-full-pseudo-checkbox.$prefix,
      tokens-mat-full-pseudo-checkbox.get-color-tokens($theme, $palette-name));
    @include token-utils.create-token-values(tokens-mat-minimal-pseudo-checkbox.$prefix,
      tokens-mat-minimal-pseudo-checkbox.get-color-tokens($theme, $palette-name));
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mat-full-pseudo-checkbox-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-full-pseudo-checkbox.$prefix, $options...);
  $mat-minimal-pseudo-checkbox-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-minimal-pseudo-checkbox.$prefix, $options...);
  @include token-utils.create-token-values(
      tokens-mat-full-pseudo-checkbox.$prefix, $mat-full-pseudo-checkbox-tokens);
  @include token-utils.create-token-values(
      tokens-mat-minimal-pseudo-checkbox.$prefix, $mat-minimal-pseudo-checkbox-tokens);
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {}

/// Outputs color theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the pseudo-checkbox: primary, secondary,
///      tertiary, or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    .mat-primary {
      @include _palette-styles($theme, primary);
    }

    // Default to the accent color. Note that the pseudo checkboxes are meant to inherit the
    // theme from their parent, rather than implementing their own theming, which is why we
    // don't attach to the `mat-*` classes. Also note that this needs to be below `.mat-primary`
    // in order to allow for the color to be overwritten if the checkbox is inside a parent that
    // has `mat-accent` and is placed inside another parent that has `mat-primary`.
    @include _palette-styles($theme, accent);
    .mat-accent {
      @include _palette-styles($theme, accent);
    }

    .mat-warn {
      @include _palette-styles($theme, warn);
    }
  }
}

/// Outputs typography theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
}

/// Outputs density theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the pseudo-checkbox: primary, secondary,
///      tertiary, or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-pseudo-checkbox') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
