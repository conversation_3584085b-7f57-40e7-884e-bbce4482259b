@use '@material/typography/typography';
@use '@material/feature-targeting/feature-targeting';
@use '@material/density/functions' as density-functions;
@use '@material/list/evolution-mixins' as mdc-list;
@use '@material/list/evolution-variables' as mdc-list-variables;
@use '../core/mdc-helpers/mdc-helpers';

// For compatibility with the non-MDC selection list, we support avatars that are
// shown at the end of the list option. This is not supported by the MDC list as the
// spec only defines avatars at the beginning of a list item. For selection list options,
// we support changing the checkbox position to `before`. This results in the avatar from
// the list start being moved to the end. Similar to MDC's `--trailing-icon` class, we
// implement a `--trailing-avatar` class that is based on the original `--leading-avatar`
// implementation. See: https://github.com/material-components/material-components-web/blob/3f342c3f4715fd3587f327ce4ea6b5dd314c5c55/packages/mdc-list/_evolution-mixins.scss#L198-L217

@mixin core-styles($query) {
  $feat-structure: feature-targeting.create-target($query, structure);

  @include mdc-helpers.disable-mdc-fallback-declarations {
    .mat-mdc-list-option-with-trailing-avatar {
      @include mdc-list.item-end-spacing(16px, $query: $query);
      @include mdc-list.item-end-size(40px, $query: $query);

      &.mdc-list-item--with-two-lines {
        .mdc-list-item__primary-text {
          @include typography.text-baseline($top: 32px, $bottom: 20px, $query: $query);
        }
      }

      .mdc-list-item__end {
        @include feature-targeting.targets($feat-structure) {
          border-radius: 50%;
        }
      }
    }
  }
}

@mixin density-styles($density-scale) {
  $one-line-tall-height: density-functions.prop-value(
    $density-config: mdc-list-variables.$one-line-item-tall-density-config,
    $density-scale: $density-scale,
    $property-name: height,
  );

  $two-line-tall-height: density-functions.prop-value(
    $density-config: mdc-list-variables.$two-line-item-tall-density-config,
    $density-scale: $density-scale,
    $property-name: height,
  );

  @include mdc-helpers.disable-mdc-fallback-declarations {
    .mat-mdc-list-option-with-trailing-avatar {
      @include mdc-list.one-line-item-height($one-line-tall-height);
      @include mdc-list.two-line-item-height($two-line-tall-height);
    }
  }
}
